import dayjs from 'dayjs';
import { isPaytmMoney } from '../../../utils/coreUtil';
import { isDarkMode } from '../../../utils/commonUtil';

export const formatFNOOIData = (data) => {
  if (!data?.data?.results) {
    return {
      labels: [],
      callsOI: [],
      putsOI: [],
    };
  }
  const formattedOI = {
    labels: [],
    callsOI: [],
    putsOI: [],
  };
  const formattedChange = {
    labels: [],
    callsOI: [],
    putsOI: [],
  };

  data.data.results.forEach((item) => {
    if (item?.stk_price) {
      formattedOI.labels.push(item?.stk_price?.toString());
      formattedOI.callsOI.push(item.call_oi);
      formattedOI.putsOI.push(item.put_oi);
    }
  });
  data.data.results.forEach((item) => {
    if (item?.stk_price) {
      formattedChange.labels.push(item?.stk_price?.toString());
      formattedChange.callsOI.push(item.call_oi_change);
      formattedChange.putsOI.push(item.put_oi_change);
    }
  });

  return {
    'open-interest': formattedOI,
    'change-in-oi': formattedChange,
  };
};

export const formatFOIndexAnalysis = (data) => {
  const { data: { meta, widget } = {} } = data;
  const attributes = widget?.attributes || [];

  const attributesMap = attributes.reduce((acc, attr) => {
    acc[attr.name] = attr.value;
    return acc;
  }, {});

  const indices = (attributesMap.FNO_IDX_ANLYS_INDICES || []).map((index) => ({
    // to do: change after API ready
    displayName: index.Name || '',
    name: index.symbol_Name || '',
    isDefault: index.default || false,
    order: index.order || 0,
    id: index.pmlid,
    security_id: parseInt(index.securityId, 10),
    exchange: index.exchange || 'NSE',
    instrument_type: index.instrument_type || 'I',
  }));

  const defaultSecondCta = {
    ...attributesMap.BUTTON_CTA2,
    cta: 'Charts',
    action: 'charts',
  };

  const buttons = [
    attributesMap.BUTTON_CTA1 || {},
    isPaytmMoney() ? attributesMap.BUTTON_CTA2 || {} : defaultSecondCta,
  ];
  let tabs = attributesMap.FNO_IDX_ANLYS_TABS || [];

  // Ensure MAX_PAIN tab is included as the last tab if not already present
  const hasMaxPainTab = tabs.some((tab) => tab.key === 'max-pain');
  if (!hasMaxPainTab) {
    tabs = [
      ...tabs,
      {
        key: 'max-pain',
        cta: 'Max Pain',
        order: tabs.length + 1,
      },
    ];
  }

  const periodTypes = tabs.find((tab) => tab.key === 'fii-dii')?.config?.range;

  return {
    data: {
      indices,
      tabs,
      buttons,
      periodTypes: periodTypes?.map((period) => ({
        ...period,
        isDefault: period.default || false,
        key: period.name.toUpperCase(),
        label: period.name,
      })) || [],
      ...meta,
    },
  };
};

export const formatFII_DIIData = (fiiData, diiData) => {
  if (!fiiData?.data?.results && !diiData?.data?.results) {
    return {
      labels: [],
      fiiData: [],
      diiData: [],
    };
  }

  const formattedData = {
    labels: [],
    fiiData: [],
    diiData: [],
  };

  fiiData.data.results.reverse().forEach((item) => {
    let formattedDate;
    if (item.trd_dt) {
      formattedDate = dayjs(item.trd_dt).format('DD MMM');
    } else if (item.month && item.year) {
      formattedDate = `${item.month < 10 ? `0${item.month}` : item.month} ${item.year}`;
    } else if (item.year) {
      formattedDate = `${item.year}`;
    }
    formattedData.labels.push(formattedDate);
    formattedData.fiiData.push(item.net);
  });
  diiData.data.results.reverse().forEach((item) => {
    formattedData.diiData.push(item.net);
  });

  return formattedData;
};

export const formatPRFIndicatorsData = (data, selectedTab) => {
  if (!data?.data?.points) {
    return [];
  }

  const { points } = data.data;
  if (selectedTab === 'atm-straddle') {
    return points.map((item) => ({
      time: dayjs(item[0], 'YYYY-MM-DD HH:mm').valueOf() / 1000,
      value: item[1] || 0,
    }));
  }

  if (selectedTab === 'pcr') {
    return points.map((item) => ({
      time: dayjs(item[0], 'YYYY-MM-DD HH:mm').valueOf() / 1000,
      value: item[2] || 0,
    }));
  }

  return [];
};

export const formatMarketMoversData = (data) => {
  if (!data?.data?.data?.length) {
    return [];
  }

  return [
    {
      label: 'Advance',
      color: isDarkMode() ? '#02A85D' : '#2CB079',
      data: data.data.data.map((item) => ({
        time: dayjs(item.start_time).valueOf() / 1000,
        value: item.gainers_count,
      })),
    },
    {
      label: 'Decline',
      color: isDarkMode() ? '#0A86BF' : '#013DA6',
      data: data.data.data.map((item) => ({
        time: dayjs(item.start_time).valueOf() / 1000,
        value: item.losers_count,
      })),
    },
  ];
};

export const formatMaxPainData = (data) => {
  if (!data?.data?.points?.length) {
    return [];
  }

  return [
    {
      label: 'Max Pain',
      color: isDarkMode() ? '#B74040' : '#EB4B4B',
      data: data.data.points.map((item) => ({
        time: dayjs(item[0], 'YYYY-MM-DD HH:mm').valueOf() / 1000,
        value: item[1] || 0,
      })),
    },
  ];
};
