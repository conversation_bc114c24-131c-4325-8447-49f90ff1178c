import { formatMaxPainData, formatMarketMoversData } from './utils';

// Mock dayjs
jest.mock('dayjs', () => {
  const originalDayjs = jest.requireActual('dayjs');
  const mockDayjs = (date, format) => {
    if (format === 'YYYY-MM-DD HH:mm') {
      return {
        valueOf: () => new Date(date).getTime(),
      };
    }
    return originalDayjs(date, format);
  };
  mockDayjs.extend = originalDayjs.extend;
  return mockDayjs;
});

// Mock isDarkMode
jest.mock('../../../utils/commonUtil', () => ({
  isDarkMode: jest.fn(() => false),
}));

describe('FOIndexAnalysisWidget Utils', () => {
  describe('formatMaxPainData', () => {
    it('should return empty array when data is null or undefined', () => {
      expect(formatMaxPainData(null)).toEqual([]);
      expect(formatMaxPainData(undefined)).toEqual([]);
      expect(formatMaxPainData({})).toEqual([]);
    });

    it('should return empty array when data.data.points is empty', () => {
      const mockData = {
        data: {
          points: [],
        },
      };
      expect(formatMaxPainData(mockData)).toEqual([]);
    });

    it('should format max pain data correctly', () => {
      const mockData = {
        data: {
          points: [
            ['2024-01-15 09:30', 18500],
            ['2024-01-15 10:00', 18550],
            ['2024-01-15 10:30', 18600],
          ],
        },
      };

      const result = formatMaxPainData(mockData);

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        label: 'Max Pain',
        color: '#EB4B4B', // Light mode color
        data: [
          {
            time: new Date('2024-01-15 09:30').getTime() / 1000,
            value: 18500,
          },
          {
            time: new Date('2024-01-15 10:00').getTime() / 1000,
            value: 18550,
          },
          {
            time: new Date('2024-01-15 10:30').getTime() / 1000,
            value: 18600,
          },
        ],
      });
    });

    it('should handle null values in points data', () => {
      const mockData = {
        data: {
          points: [
            ['2024-01-15 09:30', null],
            ['2024-01-15 10:00', 18550],
          ],
        },
      };

      const result = formatMaxPainData(mockData);

      expect(result[0].data).toEqual([
        {
          time: new Date('2024-01-15 09:30').getTime() / 1000,
          value: 0, // null should be converted to 0
        },
        {
          time: new Date('2024-01-15 10:00').getTime() / 1000,
          value: 18550,
        },
      ]);
    });

    it('should use dark mode color when isDarkMode returns true', () => {
      const { isDarkMode } = require('../../../utils/commonUtil');
      isDarkMode.mockReturnValue(true);

      const mockData = {
        data: {
          points: [['2024-01-15 09:30', 18500]],
        },
      };

      const result = formatMaxPainData(mockData);
      expect(result[0].color).toBe('#B74040'); // Dark mode color
    });
  });

  describe('formatMarketMoversData comparison', () => {
    it('should have similar structure to formatMarketMoversData', () => {
      const mockMarketData = {
        data: {
          data: [
            {
              start_time: '2024-01-15T09:30:00Z',
              gainers_count: 150,
              losers_count: 100,
            },
          ],
        },
      };

      const mockMaxPainData = {
        data: {
          points: [['2024-01-15 09:30', 18500]],
        },
      };

      const marketResult = formatMarketMoversData(mockMarketData);
      const maxPainResult = formatMaxPainData(mockMaxPainData);

      // Both should return arrays with series data
      expect(Array.isArray(marketResult)).toBe(true);
      expect(Array.isArray(maxPainResult)).toBe(true);

      // Both should have label, color, and data properties
      expect(marketResult[0]).toHaveProperty('label');
      expect(marketResult[0]).toHaveProperty('color');
      expect(marketResult[0]).toHaveProperty('data');

      expect(maxPainResult[0]).toHaveProperty('label');
      expect(maxPainResult[0]).toHaveProperty('color');
      expect(maxPainResult[0]).toHaveProperty('data');

      // Data should have time and value properties
      expect(marketResult[0].data[0]).toHaveProperty('time');
      expect(marketResult[0].data[0]).toHaveProperty('value');

      expect(maxPainResult[0].data[0]).toHaveProperty('time');
      expect(maxPainResult[0].data[0]).toHaveProperty('value');
    });
  });
});
