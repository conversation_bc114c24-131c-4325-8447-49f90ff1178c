import { useQuery } from '@tanstack/react-query';
import { makeApiGetCall, getGenericAppHeaders } from '../utils/apiUtil';
import { AxiosErrorHandler } from '../utils/errorUtils';
import { EQUITIES } from '../config/urlConfig';

export const useFNOOptionsConfig = ({ symbol, enabled = true }) => {
  const queryKey = ['fno-options-config', symbol];
  return useQuery(
    queryKey,
    async () => {
      try {
        const response = await makeApiGetCall({
          url: EQUITIES.FNO_OPTIONS_CONFIG(symbol),
          headers: getGenericAppHeaders(),
          isTimeout: true,
        });
        return response.data;
      } catch (error) {
        AxiosErrorHandler(error, false, false, false, true);
        return error;
      }
    },
    {
      enabled: enabled && !!symbol,
    },
  );
};

export const useFNOOI = ({ symbol, expiry, enabled = true }) => {
  const queryKey = ['fno-oi', symbol, expiry];
  return useQuery(
    queryKey,
    async () => {
      try {
        const response = await makeApiGetCall({
          url: EQUITIES.FNO_OI(symbol, expiry),
          headers: getGenericAppHeaders(),
          isTimeout: true,
        });
        return response.data;
      } catch (error) {
        AxiosErrorHandler(error, false, false, false, true);
        return error;
      }
    },
    {
      enabled: enabled && !!symbol && !!expiry,
    },
  );
};

export const useFII_DII = ({
  count,
  investmentCategory,
  period,
  tradeCategory,
  enabled = true,
}) => {
  const queryKey = [
    'fii-dii',
    count,
    investmentCategory,
    period,
    tradeCategory,
  ];
  return useQuery(
    queryKey,
    async () => {
      try {
        const response = await makeApiGetCall({
          url: EQUITIES.FII_DII(
            count,
            investmentCategory,
            period,
            tradeCategory,
          ),
          headers: getGenericAppHeaders(),
          isTimeout: true,
        });
        return response.data;
      } catch (error) {
        AxiosErrorHandler(error, false, false, false, true);
        return error;
      }
    },
    {
      enabled:
        enabled &&
        !!count &&
        !!investmentCategory &&
        !!period &&
        !!tradeCategory,
    },
  );
};

export const useFNOPRFIndicators = ({
  symbol,
  expiry,
  interval = '15m',
  exchange = 'NSE',
  enabled = true,
}) => {
  const queryKey = ['fno-prf-indicators', symbol, expiry, interval, exchange];
  return useQuery(
    queryKey,
    async () => {
      try {
        const response = await makeApiGetCall({
          url: EQUITIES.FNO_PRF_INDICATORS(symbol, expiry, interval, exchange),
          headers: getGenericAppHeaders(),
          isTimeout: true,
        });
        return response.data;
      } catch (error) {
        AxiosErrorHandler(error, false, false, false, true);
        return error;
      }
    },
    {
      enabled: enabled && !!symbol && !!expiry,
    },
  );
};

export const useMarketMovers = ({ index, range = 3, enabled = true }) => {
  const queryKey = ['market-movers', index, range];
  return useQuery(
    queryKey,
    async () => {
      try {
        const response = await makeApiGetCall({
          url: EQUITIES.MARKET_MOVERS(index, range),
          headers: getGenericAppHeaders(),
          isTimeout: true,
        });
        return response.data;
      } catch (error) {
        AxiosErrorHandler(error, false, false, false, true);
        return error;
      }
    },
    {
      enabled: enabled && !!index,
    },
  );
};


